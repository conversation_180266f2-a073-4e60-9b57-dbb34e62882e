package com.evydtech.chatbot.guardrails.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author:<EMAIL>
 * @Date: 2025/5/24
 * @Desc:
 */
@Slf4j
@Component
public class GuardrailsCheckInputPromptUtil {

    @Value("${chatbot.guardrails.host:}")
    public String guardrailsHost;

    public String guardrailsUrl = "/api/check_prompt";

    private OkHttpClient client;

    @PostConstruct
    public void init() {
        // 创建简单的 OkHttpClient，移除代理配置
        this.client = new OkHttpClient.Builder()
                .connectTimeout(60000, TimeUnit.MILLISECONDS)
                .readTimeout(60000, TimeUnit.MILLISECONDS)
                .writeTimeout(60000, TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .build();
        
        log.info("GUARDRAILS_CLIENT_INITIALIZED|WITHOUT_PROXY");
    }

    /**
     * 检查 输入的信息是否合法
     * @param message 需要检查的消息
     * @return 是否安全
     */
    public  boolean checkInputPrompt(String message) {
        log.info("target: {}", SpringUtil.getActiveProfile());
        if (List.of("local", "test", "preview").contains(SpringUtil.getActiveProfile())) {
//        if (List.of("local").contains(SpringUtil.getActiveProfile())) {
            return true;
        }
        try {
            // 1. 构建请求参数
            Map<String, Object> map = new HashMap<>();
            map.put("prompt", message);

            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, JSONUtil.toJsonStr(map));
            
            // 2. 构建请求
            String url = StrUtil.format("{}{}", guardrailsHost, guardrailsUrl);
//            String url = "http://0.0.0.0:8080/api/check_prompt";
            log.info("开始检查输入提示词，请求URL: {}", url);
            log.info("请求参数: {}", JSONUtil.toJsonStr(map));
            
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 3. 发送请求并处理响应
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("Guardrails响应: {}", responseBody);
                
                if (response.isSuccessful()) {
                    // 4. 解析响应
                    Map<String, Object> result = JSONUtil.parseObj(responseBody);
                    boolean isSafe = (boolean) result.get("is_safe");
                    // 处理 BigDecimal 到 Double 的转换
                    Object confidenceObj = result.get("confidence");
                    double confidence;
                    if (confidenceObj instanceof BigDecimal) {
                        confidence = ((BigDecimal) confidenceObj).doubleValue();
                    } else {
                        confidence = (double) confidenceObj;
                    }
                    
                    // 5. 记录详细信息
                    Map<String, Object> details = (Map<String, Object>) result.get("details");
                    log.info("检查结果 - 是否安全: {}, 置信度: {}, 模型: {}", 
                        isSafe, 
                        confidence,
                        details.get("model_name"));
                    
                    return isSafe;
                } else {
                    log.error("Guardrails请求失败，状态码: {}, 响应: {}", response.code(), responseBody);
                }
            }
        } catch (Exception e) {
            log.error("检查输入提示词时发生异常", e);
        }
        return false;
    }
}
