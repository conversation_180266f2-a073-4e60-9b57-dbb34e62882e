package com.evydtech.chatbot.weaviate.config;

import io.weaviate.client.Config;
import io.weaviate.client.WeaviateClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: qiang<PERSON><EMAIL>
 * @Date: 2023/05/01/16:21
 * @Description: 配置类
 */
@Configuration
public class WeaviateConfig {


    @Value("${weaviate.host:***********:8080}")
    private String host;

    @Value("${weaviate.scheme:http}")
    private String scheme;

    @Bean
    public WeaviateClient weaviateClient() {
        Config config = new Config(scheme, host);
        return new WeaviateClient(config);
    }


}

