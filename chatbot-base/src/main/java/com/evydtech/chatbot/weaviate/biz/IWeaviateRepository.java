package com.evydtech.chatbot.weaviate.biz;

import com.evydtech.chatbot.weaviate.domain.WeaviateQueryDto;
import io.weaviate.client.v1.filters.WhereFilter;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/05/01/16:21
 * @Description: Weaviate操作封装
 */
public interface IWeaviateRepository<T> {


    /**
     * @param objects :  Weaviate objects写入的数据
     * @return : io.weaviate.client.base.Result<io.weaviate.client.v1.batch.model.ObjectGetResponse[]>
     * <AUTHOR>
     * @Description 批量写入数据
     * @Date 2023/5/3 20:25
     */
    Boolean batch(List<T> objects);

    /**
     * @param queryDTO :  Weaviate objects操作时的查询参数
     * @return : java.util.List<T>
     * <AUTHOR>
     * @Description 根据URL精确匹配
     * @Date 2023/5/4 17:50
     */
    List<T> where(WeaviateQueryDto queryDTO);

    /**
     * @param queryDTO :  Weaviate objects操作时的查询参数
     * @return : java.util.List<T>
     * <AUTHOR>
     * @Description 根据name模糊匹配
     * @Date 2023/5/4 17:50
     */
    List<T> like(WeaviateQueryDto queryDTO);

    /**
     * @param queryDTO :   Weaviate objects操作时的查询参数
     * @return : java.util.List<com.evydtech.chatbot.weaviate.domain.GNCProducts>
     * <AUTHOR>
     * @Description 矢量查询
     * @Date 2023/5/5 18:09
     */
    List<T> nearText(WeaviateQueryDto queryDTO);

    /**
     * 精准查询
     *
     * @param queryDTO WeaviateQueryDto
     * @return List<T>
     */
    List<T> preciseQueryByDto(WeaviateQueryDto queryDTO);

    /**
     * @param queryDTO    :   objects操作时的查询参数
     * @param whereFilter : 过滤条件(可包含like,and,gt,lt,gte,lte,ne,in,notIn等操作.)
     * @return : java.util.List<com.evydtech.chatbot.weaviate.domain.GNCProducts>
     * <AUTHOR>
     * @Description 含有过滤条件和nearText的查询 {@link <a href="https://weaviate.io/developers/weaviate/api/graphql/filters#multiple-operands">...</a>}
     * @Date 2023/5/20 13:36
     */
    List<T> queryByWhereAndNearText(WeaviateQueryDto queryDTO, WhereFilter whereFilter);

}

