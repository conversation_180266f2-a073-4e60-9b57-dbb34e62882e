package com.evydtech.chatbot.weaviate.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import io.weaviate.client.v1.data.model.WeaviateObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @Author: <EMAIL>
 * @Date: 2023/05/03/19:10
 * @Description: GNC的文章
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GNCArticles extends WeaviateBaseObject {

    public static final String GNC_ARTICLES_CLASS_NAME = "GNCArticlesV3";

    /*以下是自定义属性*/
    private String category;
    private String content;
    private String paragraphTitle;
    private String title;
    private String url;


    public static GNCArticles of(WeaviateObject weaviateObject) {
        return Optional.ofNullable(weaviateObject)
                .map(obj -> {
                    GNCArticles gncArticles = new GNCArticles();
                    gncArticles.setId(obj.getId());
                    gncArticles.setClassName(obj.getClassName());
                    gncArticles.setCreationTimeUnix(obj.getCreationTimeUnix());
                    gncArticles.setLastUpdateTimeUnix(obj.getLastUpdateTimeUnix());
                    gncArticles.setProperties(obj.getProperties());
                    gncArticles.set_additional(obj.getAdditional());
                    gncArticles.setVector(obj.getVector());
                    gncArticles.setVectorWeights(obj.getVectorWeights());
                    gncArticles.setCategory(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("category"))).orElse(null));
                    gncArticles.setContent(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("content"))).orElse(null));
                    gncArticles.setParagraphTitle(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("paragraph_title"))).orElse(null));
                    gncArticles.setTitle(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("title"))).orElse(null));
                    gncArticles.setUrl(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("url"))).orElse(null));
                    return gncArticles;
                }).orElse(null);
    }

    public static List<WeaviateObject> tos(List<GNCArticles> objects) {
        if (CollUtil.isEmpty(objects)) {
            return Collections.emptyList();
        }
        return objects
                .stream()
                .map(GNCArticles::to)
                .collect(Collectors.toList());
    }

    public static WeaviateObject to(GNCArticles articles) {
        return Optional.ofNullable(articles)
                .map(gncArticles -> WeaviateObject.builder()
                        .id(gncArticles.getId())
                        .className(GNC_ARTICLES_CLASS_NAME)
                        .additional(gncArticles.get_additional())
                        .creationTimeUnix(gncArticles.getCreationTimeUnix())
                        .lastUpdateTimeUnix(gncArticles.getLastUpdateTimeUnix())
                        .vector(gncArticles.getVector())
                        .vectorWeights(gncArticles.getVectorWeights())
                        .properties(gncArticles.getProperties())
                        .build()
                ).orElse(null);
    }

}
