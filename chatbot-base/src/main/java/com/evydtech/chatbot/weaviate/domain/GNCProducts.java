package com.evydtech.chatbot.weaviate.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import io.weaviate.client.v1.data.model.WeaviateObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @Author: <EMAIL>
 * @Date: 2023/05/03/19:10
 * @Description: GNC的文章
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GNCProducts extends WeaviateBaseObject {

    public static String GNC_PRODUCTS_CLASS_NAME;

    static {
        GNC_PRODUCTS_CLASS_NAME = SpringUtil.getProperty("weaviate.productdb");
    }

    /*以下是自定义属性*/
    private String brand;
    private String detail;
    private String howToUse;
    private String ingredient;
    private String name;
    private String url;
    private String price;
    private String imageUrl;
    private Double weight;
    private Double bestseller;
    private String summary;
    private String identity;
    // 口味
    private String flavor;
    // 标签
    private String label;

    public static GNCProducts of(WeaviateObject weaviateObject) {
        return Optional.ofNullable(weaviateObject)
                .map(obj -> {
                    GNCProducts gncProducts = new GNCProducts();
                    gncProducts.setId(obj.getId());
                    gncProducts.setClassName(obj.getClassName());
                    gncProducts.setCreationTimeUnix(obj.getCreationTimeUnix());
                    gncProducts.setLastUpdateTimeUnix(obj.getLastUpdateTimeUnix());
                    gncProducts.setProperties(obj.getProperties());
                    gncProducts.set_additional(obj.getAdditional());
                    gncProducts.setVector(obj.getVector());
                    gncProducts.setVectorWeights(obj.getVectorWeights());
                    gncProducts.setBrand(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("brand"))).orElse(null));
                    gncProducts.setDetail(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("detail"))).orElse(null));
                    gncProducts.setHowToUse(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("how_to_use"))).orElse(null));
                    gncProducts.setIngredient(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("ingredient"))).orElse(null));
                    gncProducts.setName(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("name"))).orElse(null));
                    gncProducts.setUrl(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("url"))).orElse(null));
                    gncProducts.setImageUrl(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("image_url"))).orElse(null));
                    gncProducts.setPrice(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("price"))).orElse(null));
                    gncProducts.setWeight(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toDouble(p.get("weight"))).orElse(null));
                    gncProducts.setBestseller(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toDouble(p.get("bestseller"))).orElse(null));
                    gncProducts.setSummary(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("summary"))).orElse(null));
                    gncProducts.setIdentity(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("identity"))).orElse(null));
                    return gncProducts;
                }).orElse(null);
    }

    public static List<WeaviateObject> tos(List<GNCProducts> objects) {
        if (CollUtil.isEmpty(objects)) {
            return Collections.emptyList();
        }
        return objects
                .stream()
                .map(GNCProducts::to)
                .collect(Collectors.toList());
    }

    public static WeaviateObject to(GNCProducts products) {
        return Optional.ofNullable(products)
                .map(gncProducts -> WeaviateObject.builder()
                        .id(gncProducts.getId())
                        .className(GNC_PRODUCTS_CLASS_NAME)
                        .additional(gncProducts.get_additional())
                        .creationTimeUnix(gncProducts.getCreationTimeUnix())
                        .lastUpdateTimeUnix(gncProducts.getLastUpdateTimeUnix())
                        .vector(gncProducts.getVector())
                        .vectorWeights(gncProducts.getVectorWeights())
                        .properties(gncProducts.getProperties())
                        .build()
                ).orElse(null);
    }
}
