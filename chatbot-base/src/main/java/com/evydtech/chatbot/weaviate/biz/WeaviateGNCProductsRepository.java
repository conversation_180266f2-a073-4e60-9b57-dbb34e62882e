package com.evydtech.chatbot.weaviate.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.evydtech.chatbot.weaviate.constant.VdbSchemaProductConstant;
import com.evydtech.chatbot.weaviate.domain.GNCProducts;
import com.evydtech.chatbot.weaviate.domain.WeaviateQueryDto;
import io.weaviate.client.WeaviateClient;
import io.weaviate.client.base.Result;
import io.weaviate.client.v1.batch.model.ObjectGetResponse;
import io.weaviate.client.v1.data.model.WeaviateObject;
import io.weaviate.client.v1.data.replication.model.ConsistencyLevel;
import io.weaviate.client.v1.filters.Operator;
import io.weaviate.client.v1.filters.WhereFilter;
import io.weaviate.client.v1.graphql.model.GraphQLResponse;
import io.weaviate.client.v1.graphql.query.argument.NearTextArgument;
import io.weaviate.client.v1.graphql.query.argument.SortArgument;
import io.weaviate.client.v1.graphql.query.fields.Field;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;


/**
 * @Author: <EMAIL>
 * @Date: 2023/05/01/16:21
 * @Description: Weaviate操作封装
 */
@Slf4j
@Repository(WeaviateGNCProductsRepository.BEAN_NAME)
public class WeaviateGNCProductsRepository extends AbstractWeaviateRepository<GNCProducts> {

    public static final String BEAN_NAME = "weaviateGNCProductsRepository";

    @Value("${weaviate.productdb:}")
    private String weaviateProductDB;

    @Autowired
    private WeaviateClient weaviateClient;


    public List<GNCProducts> page(WeaviateQueryDto queryDTO) {
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateProductDB)
                .withFields(getQueryField())
                .withLimit(queryDTO.getLimit())
                .withOffset(queryDTO.getOffset())
                .run();
        return graphQLResponse2List(result, GNCProducts.class, weaviateProductDB);
    }


    @Override
    public List<GNCProducts> where(WeaviateQueryDto queryDTO) {
        WhereFilter where = WhereFilter.builder()
                .path(new String[]{"url"})
                .operator(Operator.Equal)
                .valueString(queryDTO.getValueText())
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateProductDB)
                .withFields(getQueryField())
                .withWhere(where)
                .run();
        return graphQLResponse2List(result, GNCProducts.class, weaviateProductDB);
    }


    @Override
    public List<GNCProducts> like(WeaviateQueryDto queryDTO) {
        WhereFilter where = WhereFilter.builder()
                .path(new String[]{"name"})
                .operator(Operator.Like)
                .valueText(StrUtil.format("*{}*", queryDTO.getValueText()))
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateProductDB)
                .withFields(getQueryField())
                .withWhere(where)
                .run();
        return graphQLResponse2List(result, GNCProducts.class, weaviateProductDB);
    }


    @Override
    public List<GNCProducts> nearText(WeaviateQueryDto queryDTO) {
        log.info("VECTOR_DB_SEARCH_PRODUCT_NEAR_TEXT_QO|{}", JSON.toJSONString(queryDTO));
        SortArgument[] sortArgumentArray = null;
        if (CollectionUtil.isNotEmpty(queryDTO.getSortArgumentList())) {
            sortArgumentArray = queryDTO.getSortArgumentList().toArray(new SortArgument[0]);
        }

        NearTextArgument nearTextArgument = NearTextArgument.builder()
                .concepts(new String[]{queryDTO.getNearText()})
                .distance(0.7f)
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateProductDB)
                .withFields(getQueryField())
                .withNearText(nearTextArgument)
                .withLimit(Objects.isNull(queryDTO.getLimit()) ? 3 : queryDTO.getLimit())
                .withSort(sortArgumentArray)
                .run();
        return graphQLResponse2List(result, GNCProducts.class, weaviateProductDB);
    }

    @Override
    public Boolean batch(List objects) {
        Result<ObjectGetResponse[]> result = weaviateClient.batch()
                .objectsBatcher()
                .withObjects(ArrayUtil.toArray(GNCProducts.tos(objects), WeaviateObject.class))
                .withConsistencyLevel(ConsistencyLevel.ALL)  // default QUORUM
                .run();
        if (result == null || result.hasErrors()) {
            log.error("[WeaviateGNCProductsRepository][batch] error {}", JSONUtil.toJsonStr(result.getError()));
            return false;
        }
        return true;
    }

    /**
     * 精准查询
     *
     * @param queryDTO WeaviateQueryDto
     * @return List<T>
     */
    @Override
    public List<GNCProducts> preciseQueryByDto(final WeaviateQueryDto queryDTO) {
        // Build query fields
        Field name = Field.builder().name(VdbSchemaProductConstant.NAME).build();
        Field brand = Field.builder().name(VdbSchemaProductConstant.BRAND).build();
        Field howToUse = Field.builder().name(VdbSchemaProductConstant.HOW_TO_USE).build();
        Field ingredient = Field.builder().name(VdbSchemaProductConstant.INGREDIENT).build();
        Field url = Field.builder().name(VdbSchemaProductConstant.URL).build();
        Field detail = Field.builder().name(VdbSchemaProductConstant.DETAIL).build();
        Field imageUrl = Field.builder().name(VdbSchemaProductConstant.IMG_URL).build();
        Field price = Field.builder().name(VdbSchemaProductConstant.PRICE).build();
        Field[] fields = new Field[]{
                name, brand, howToUse, ingredient, url, detail, imageUrl, price
        };

        // Build query path
        String[] queryPaths = queryDTO.getProductPathList();
        WhereFilter where = WhereFilter.builder()
                .path(queryPaths)
                .operator(Operator.Equal)
                .valueString(queryDTO.getProductUrl())
                .build();

        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateProductDB)
                .withFields(fields)
                .withLimit(1)
                .withWhere(where)
                .run();
        return graphQLResponse2List(result, GNCProducts.class, weaviateProductDB);
    }


    @Override
    public List<GNCProducts> queryByWhereAndNearText(WeaviateQueryDto queryDTO, WhereFilter whereFilter) {
        log.info("VECTOR_DB_SEARCH_PRODUCT_NEAR_AND_WHERE_TEXT_QO|QUERY|{}|WHERE|{}", JSON.toJSONString(queryDTO), JSON.toJSONString(whereFilter));
        SortArgument[] sortArgumentArray = null;
        if (CollectionUtil.isNotEmpty(queryDTO.getSortArgumentList())) {
            sortArgumentArray = queryDTO.getSortArgumentList().toArray(new SortArgument[0]);
        }

        NearTextArgument nearTextArgument = NearTextArgument.builder()
                .concepts(new String[]{queryDTO.getNearText()})
                .distance(0.7f)
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateProductDB)
                .withFields(getQueryField())
                .withWhere(whereFilter)
                .withNearText(nearTextArgument)
                .withLimit(Objects.isNull(queryDTO.getLimit()) ? 25 : queryDTO.getLimit())
                .withSort(sortArgumentArray)
                .run();
        return graphQLResponse2List(result, GNCProducts.class, weaviateProductDB);
    }

    @Override
    protected Field[] getQueryField() {
        Field name = Field.builder().name("name").build();
        Field brand = Field.builder().name("brand").build();
        Field howToUse = Field.builder().name("how_to_use").build();
        Field ingredient = Field.builder().name("ingredient").build();
        Field url = Field.builder().name("url").build();
        Field detail = Field.builder().name("detail").build();
        Field imageUrl = Field.builder().name("image_url").build();
        Field price = Field.builder().name("price").build();
        Field label = Field.builder().name("label").build();
        Field weight = Field.builder().name("weight").build();
        Field bestseller = Field.builder().name("bestseller").build();
        Field summary = Field.builder().name("summary").build();
        Field identity = Field.builder().name("identity").build();
        Field _additional = Field.builder()
                .name("_additional")
                .fields(new Field[]{
                        Field.builder().name("certainty").build(), // only supported if distance==cosine
                        Field.builder().name("distance").build()   // always supported
                }).build();
        return new Field[]{name, brand, label, howToUse, url, imageUrl, price,
                detail, ingredient, weight, bestseller, summary, identity, _additional};
    }

}

