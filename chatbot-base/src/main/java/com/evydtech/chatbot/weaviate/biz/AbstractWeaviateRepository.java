package com.evydtech.chatbot.weaviate.biz;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.evydtech.chatbot.weaviate.domain.WeaviateQueryDto;
import io.weaviate.client.base.Result;
import io.weaviate.client.v1.filters.WhereFilter;
import io.weaviate.client.v1.graphql.model.GraphQLResponse;
import io.weaviate.client.v1.graphql.query.fields.Field;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/05/20/11:40
 * @Description: 向量库查询基类
 */
@Slf4j
public abstract class AbstractWeaviateRepository<T> implements IWeaviateRepository {


    /**
     * @param objects :  Weaviate objects写入的数据
     * @return : io.weaviate.client.base.Result<io.weaviate.client.v1.batch.model.ObjectGetResponse[]>
     * <AUTHOR>
     * @Description 批量写入数据
     * @Date 2023/5/3 20:25
     */
    public Boolean batch(List objects) {
        return false;
    }


    /**
     * @param queryDTO :  Weaviate objects操作时的查询参数
     * @return : java.util.List<T>
     * <AUTHOR>
     * @Description 根据URL精确匹配
     * @Date 2023/5/4 17:50
     */
    public List<T> where(WeaviateQueryDto queryDTO) {
        return Collections.emptyList();
    }

    /**
     * @param queryDTO :  Weaviate objects操作时的查询参数
     * @return : java.util.List<T>
     * <AUTHOR>
     * @Description 根据name模糊匹配
     * @Date 2023/5/4 17:50
     */
    public List<T> like(WeaviateQueryDto queryDTO) {
        return Collections.emptyList();
    }

    /**
     * @param queryDTO :   Weaviate objects操作时的查询参数
     * @return : java.util.List<com.evydtech.chatbot.weaviate.domain.GNCProducts>
     * <AUTHOR>
     * @Description 矢量查询
     * @Date 2023/5/5 18:09
     */
    public List<T> nearText(WeaviateQueryDto queryDTO) {
        return Collections.emptyList();
    }

    /**
     * 精准查询
     *
     * @param queryDTO WeaviateQueryDto
     * @return List<T>
     */
    public List<T> preciseQueryByDto(WeaviateQueryDto queryDTO) {
        return Collections.emptyList();
    }

    public List<T> queryByWhereAndNearText(WeaviateQueryDto queryDTO, WhereFilter whereFilter) {
        return Collections.emptyList();
    }


    /**
     * @return : io.weaviate.client.v1.graphql.query.fields.Field[] 字段数组
     * <AUTHOR>
     * @Description 查询向量库中商品的字段
     * @Date 2023/5/20 11:29
     */
    protected abstract Field[] getQueryField();


    /**
     * @param result :  查询向量库的结果
     * @return : java.util.List<com.evydtech.chatbot.weaviate.domain.GNCProducts> 商品集合
     * <AUTHOR>
     * @Description 将查询向量库的结果转换成商品集合
     * @Date 2023/5/20 11:30
     */
    protected List<T> graphQLResponse2List(Result<GraphQLResponse> result, Class<T> clazz, String weaviateClassName) {
        if (result == null || result.hasErrors() || result.getError() != null || ArrayUtil.isNotEmpty(result.getResult().getErrors())) {
            log.error("Weaviate Repository error {}", JSONUtil.toJsonStr(result));
            return new ArrayList<>();
        }
        String jsonStr = JSONUtil.toJsonStr(result.getResult().getData());
        JSONArray jsonArray = (JSONArray) JSONUtil.parseObj(jsonStr).getByPath("Get." + weaviateClassName);
        return jsonArray.toList(clazz);
    }


}
