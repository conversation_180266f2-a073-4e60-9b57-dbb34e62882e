package com.evydtech.chatbot.weaviate.domain;

import io.weaviate.client.v1.graphql.query.argument.SortArgument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2023/05/03/16:49
 * @Description: Weaviate objects操作时的查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class WeaviateQueryDto {
    /**
     * 使用类名按类列出对象
     */
    private String className;
    /**
     * ID
     */
    private String id;
    /**
     * 要返回的数据对象的最大数量。默认 25。
     */
    private Integer limit;
    /**
     * 返回对象的偏移量（返回对象的起始索引）。不能与 after 一起使用。应与 limit 结合使用。
     */
    private Integer offset;
    /**
     * 对象的 ID，之后要检索（即非包含 ID）对象。必须与 class 一起使用
     * 不能与 offset 或 sort 一起使用。
     * 应与 limit 结合使用。
     */
    private String after;
    /**
     * 包括其他信息，例如分类信息。 additional
     * 允许的值包括： classification 、 vector 、 featureProjection 和其他模块特定的附加属性。
     */
    private String include;
    /**
     * 排序字段
     */
    private List<SortArgument> sortArgumentList;

    /**
     * 排序依据的顺序。
     * 可能的值： asc （默认）和 desc 。
     * 应与 sort 结合使用。
     */
    private String order;
    /**
     * properties 属性
     */
    private Map<String, Object> properties;

    private String valueText;

    private String nearText;

    /**
     * ProductUrl
     */
    private String productUrl;

    /**
     * ProductPathList
     */
    String[] productPathList;

    /**
     * SetNearText
     *
     * @param nearText String
     */
    public void setNearText(String nearText) {
        if (nearText != null) {
            String regex = "[^a-zA-Z0-9\\-]";
            nearText = nearText.replaceAll(regex, " ");
        }
        this.nearText = nearText;
    }
}
