package com.evydtech.chatbot.weaviate.domain;

import lombok.Data;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2023/05/03/20:09
 * @Description:
 */
@Data
public class WeaviateBaseObject {

    /*以下是通用属性*/
    private String id;
    private String className;
    private Long creationTimeUnix;
    private Long lastUpdateTimeUnix;
    private Map<String, Object> properties;
    private Map<String, Object> _additional;
    private Float[] vector;
    private Object vectorWeights;


}
