package com.evydtech.chatbot.weaviate.biz;

import com.evydtech.chatbot.weaviate.domain.MedicalKnowledge;
import com.evydtech.chatbot.weaviate.domain.WeaviateQueryDto;
import io.weaviate.client.WeaviateClient;
import io.weaviate.client.base.Result;
import io.weaviate.client.v1.filters.Operator;
import io.weaviate.client.v1.filters.WhereFilter;
import io.weaviate.client.v1.graphql.model.GraphQLResponse;
import io.weaviate.client.v1.graphql.query.argument.NearTextArgument;
import io.weaviate.client.v1.graphql.query.fields.Field;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;


@Slf4j
@Repository(WeaviateMedicalKnowledgeRepository.BEAN_NAME)
public class WeaviateMedicalKnowledgeRepository extends AbstractWeaviateRepository<MedicalKnowledge> {

    public static final String BEAN_NAME = "weaviateMedicalKnowledgeRepository";

    @Autowired
    private WeaviateClient weaviateClient;

    @Value("${weaviate.medicaldb:MedicalKnowledgeDB}")
    private String weaviateMedicalDB;


    @Override
    public List<MedicalKnowledge> where(WeaviateQueryDto queryDTO) {
        WhereFilter where = WhereFilter.builder()
                .path(new String[]{"url"})
                .operator(Operator.Equal)
                .valueString(queryDTO.getValueText())
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateMedicalDB)
                .withFields(getQueryField())
                .withWhere(where)
                .run();
        return graphQLResponse2List(result, MedicalKnowledge.class, weaviateMedicalDB);
    }


    @Override
    public List<MedicalKnowledge> nearText(WeaviateQueryDto queryDTO) {
        NearTextArgument nearTextArgument = NearTextArgument.builder()
                .concepts(new String[]{queryDTO.getNearText()})
                .distance(0.7f)
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateMedicalDB)
                .withFields(getQueryField())
                .withNearText(nearTextArgument)
                .withLimit(queryDTO.getLimit())
                .run();
        return graphQLResponse2List(result, MedicalKnowledge.class, weaviateMedicalDB);
    }


    @Override
    protected Field[] getQueryField() {
        Field category = Field.builder().name("category").build();
        // Field medicalClassification = Field.builder().name("medicalClassification").build();
        Field content = Field.builder().name("content").build();
        Field paragraphTitle = Field.builder().name("paragraph_title").build();
        Field title = Field.builder().name("title").build();
        Field url = Field.builder().name("url").build();
        Field _additional = Field.builder()
                .name("_additional")
                .fields(new Field[]{
                        Field.builder().name("certainty").build(), // only supported if distance==cosine
                        Field.builder().name("distance").build()   // always supported
                }).build();
        return new Field[]{category, content, paragraphTitle, title, url, _additional};
    }


}
