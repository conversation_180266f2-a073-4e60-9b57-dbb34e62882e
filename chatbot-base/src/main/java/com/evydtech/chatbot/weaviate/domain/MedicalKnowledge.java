package com.evydtech.chatbot.weaviate.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import io.weaviate.client.v1.data.model.WeaviateObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MedicalKnowledge extends WeaviateBaseObject{
    public static final String MEDICAL_CLASS_NAME = "MedicalKnowledgeDB";

    /*以下是自定义属性*/
    private String category;
    //private String medicalClassification;
    private String content;
    private String paragraphTitle;
    private String title;
    private String url;

    public static MedicalKnowledge of(WeaviateObject weaviateObject) {
        return Optional.ofNullable(weaviateObject)
                .map(obj -> {
                    MedicalKnowledge medicalKnowledge = new MedicalKnowledge();
                    medicalKnowledge.setId(obj.getId());
                    medicalKnowledge.setClassName(obj.getClassName());
                    medicalKnowledge.setCreationTimeUnix(obj.getCreationTimeUnix());
                    medicalKnowledge.setLastUpdateTimeUnix(obj.getLastUpdateTimeUnix());
                    medicalKnowledge.setProperties(obj.getProperties());
                    medicalKnowledge.set_additional(obj.getAdditional());
                    medicalKnowledge.setVector(obj.getVector());
                    medicalKnowledge.setVectorWeights(obj.getVectorWeights());
                    medicalKnowledge.setCategory(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("category"))).orElse(null));
                    //medicalKnowledge.setMedicalClassification(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("medicalClassification"))).orElse(null));
                    medicalKnowledge.setContent(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("content"))).orElse(null));
                    medicalKnowledge.setParagraphTitle(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("paragraph_title"))).orElse(null));
                    medicalKnowledge.setTitle(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("title"))).orElse(null));
                    medicalKnowledge.setUrl(Optional.ofNullable(obj.getProperties()).map(p -> Convert.toStr(p.get("url"))).orElse(null));
                    return medicalKnowledge;
                }).orElse(null);
    }

    public static List<WeaviateObject> tos(List<MedicalKnowledge> objects) {
        if (CollUtil.isEmpty(objects)) {
            return Collections.emptyList();
        }
        return objects
                .stream()
                .map(MedicalKnowledge::to)
                .collect(Collectors.toList());
    }

    public static WeaviateObject to(MedicalKnowledge articles) {
        return Optional.ofNullable(articles)
                .map(medicalKnowledge -> WeaviateObject.builder()
                        .id(medicalKnowledge.getId())
                        .className(MEDICAL_CLASS_NAME)
                        .additional(medicalKnowledge.get_additional())
                        .creationTimeUnix(medicalKnowledge.getCreationTimeUnix())
                        .lastUpdateTimeUnix(medicalKnowledge.getLastUpdateTimeUnix())
                        .vector(medicalKnowledge.getVector())
                        .vectorWeights(medicalKnowledge.getVectorWeights())
                        .properties(medicalKnowledge.getProperties())
                        .build()
                ).orElse(null);
    }
}
