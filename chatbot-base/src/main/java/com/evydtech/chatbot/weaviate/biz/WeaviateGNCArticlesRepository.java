package com.evydtech.chatbot.weaviate.biz;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.evydtech.chatbot.weaviate.domain.GNCArticles;
import com.evydtech.chatbot.weaviate.domain.WeaviateQueryDto;
import io.weaviate.client.WeaviateClient;
import io.weaviate.client.base.Result;
import io.weaviate.client.v1.batch.model.ObjectGetResponse;
import io.weaviate.client.v1.data.model.WeaviateObject;
import io.weaviate.client.v1.data.replication.model.ConsistencyLevel;
import io.weaviate.client.v1.filters.Operator;
import io.weaviate.client.v1.filters.WhereFilter;
import io.weaviate.client.v1.graphql.model.GraphQLResponse;
import io.weaviate.client.v1.graphql.query.argument.NearTextArgument;
import io.weaviate.client.v1.graphql.query.fields.Field;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;


/**
 * @Author: <EMAIL>
 * @Date: 2023/05/01/16:21
 * @Description: Weaviate操作封装
 */
@Slf4j
@Repository(WeaviateGNCArticlesRepository.BEAN_NAME)
public class WeaviateGNCArticlesRepository extends AbstractWeaviateRepository<GNCArticles> {

    public static final String BEAN_NAME = "weaviateGNCArticlesRepository";

    @Autowired
    private WeaviateClient weaviateClient;

    @Value("${weaviate.articlesdb:GNCArticlesV3}")
    private String weaviateArticlesDB;


    @Override
    public Boolean batch(List objects) {
        Result<ObjectGetResponse[]> result = weaviateClient.batch()
                .objectsBatcher()
                .withObjects(ArrayUtil.toArray(GNCArticles.tos(objects), WeaviateObject.class))
                .withConsistencyLevel(ConsistencyLevel.ALL)  // default QUORUM
                .run();
        if (result == null || result.hasErrors()) {
            log.error("[WeaviateGNCArticlesRepository][batch] error {}", JSONUtil.toJsonStr(result.getError()));
            return false;
        }
        return true;
    }

    @Override
    public List<GNCArticles> where(WeaviateQueryDto queryDTO) {
        WhereFilter where = WhereFilter.builder()
                .path(new String[]{"url"})
                .operator(Operator.Equal)
                .valueString(queryDTO.getValueText())
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateArticlesDB)
                .withFields(getQueryField())
                .withWhere(where)
                .run();
        return graphQLResponse2List(result, GNCArticles.class, weaviateArticlesDB);
    }

    @Override
    public List<GNCArticles> like(WeaviateQueryDto queryDTO) {
        WhereFilter where = WhereFilter.builder()
                .path(new String[]{"title"})
                .operator(Operator.Like)
                .valueText(StrUtil.format("*{}*", queryDTO.getValueText()))
                .build();

        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateArticlesDB)
                .withFields(getQueryField())
                .withWhere(where)
                .run();
        return graphQLResponse2List(result, GNCArticles.class, weaviateArticlesDB);
    }

    @Override
    public List<GNCArticles> nearText(WeaviateQueryDto queryDTO) {
        NearTextArgument nearTextArgument = NearTextArgument.builder()
                .concepts(new String[]{queryDTO.getNearText()})
                .distance(0.7f)
                .build();
        Result<GraphQLResponse> result = weaviateClient.graphQL().get()
                .withClassName(weaviateArticlesDB)
                .withFields(getQueryField())
                .withNearText(nearTextArgument)
                .withLimit(Objects.isNull(queryDTO.getLimit()) ? 25 : queryDTO.getLimit())
                .run();
        return graphQLResponse2List(result, GNCArticles.class, weaviateArticlesDB);
    }


    @Override
    protected Field[] getQueryField() {
        Field category = Field.builder().name("category").build();
        Field content = Field.builder().name("content").build();
        Field paragraphTitle = Field.builder().name("paragraph_title").build();
        Field title = Field.builder().name("title").build();
        Field url = Field.builder().name("url").build();
        Field _additional = Field.builder()
                .name("_additional")
                .fields(new Field[]{
                        Field.builder().name("certainty").build(), // only supported if distance==cosine
                        Field.builder().name("distance").build()   // always supported
                }).build();
        return new Field[]{category, content, paragraphTitle, title, url, _additional};
    }
}

