/**
 * SuggestionVo
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2024/9/12 17:24
 * @license: Copyright © 2012 - 2024 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.suggestion.domain.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SuggestionVo implements Serializable {
    private final static long serialVersionUID = 1L;

    protected Opening opening;
    protected List<Question> questions;
    protected AiGc aiGc;

    @Data
    public static class Opening implements Serializable {
        private String title;
        private String subTitle;
        private String summary;
        private String coverUrl;
        private String jumpUrl;
    }

    @Data
    public static class Question implements Serializable {
        private String title;
        private String subTitle;
        private String summary;
        private String coverUrl;
        private String jumpUrl;
    }

    @Data
    public static class AiGc implements Serializable {
        private String text;
    }
}
