/**
 * OpenAiHeaderInterceptor
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/7/13 18:23
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.interceptor.openai;

import com.evydtech.chatbot.tool.config.AiPlatformConfig;
import com.evydtech.chatbot.tool.contant.AiConstant;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class OpenAiHeaderInterceptor implements Interceptor {
    @Autowired
    AiPlatformConfig aiPlatformConfig;

    @Override
    public Response intercept(final Chain chain) throws IOException {
        Request sourceRequest = chain.request();

        //Request modifiedRequest = sourceRequest.newBuilder()
        //        .addHeader(AiConstant.OPEN_AI_HEADER_KEY_ORG, this.aiPlatformConfig.getOpenAiOrganization())
        //        .build();
        return chain.proceed(sourceRequest);
    }
}
