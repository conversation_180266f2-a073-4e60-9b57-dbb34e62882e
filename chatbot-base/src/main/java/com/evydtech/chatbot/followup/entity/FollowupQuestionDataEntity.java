/**
 * FollowupTaskEntity
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/5/16 19:36
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.entity;

import com.evydtech.chatbot.common.constant.MongoDbConstant;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Data
@Document(MongoDbConstant.MONGO_DB_COLLECTION_NAME_FOLLOWUP_QUESTION_DATA)
public class FollowupQuestionDataEntity {
    @Field("_id")
    private String id;

    /**
     * BizId
     */
    private String bizId;

    /**
     * UserBizId
     */
    private String userBizId;

    /**
     * QuestionBizId
     */
    private String questionBizId;

    /**
     * Followup type
     * @see com.evydtech.chatbot.followup.enums.FollowupTypeEnum
     */
    private Integer type;

    /**
     * No
     */
    private Integer no;

    /**
     * Title
     */
    private String title;

    /**
     * Result
     */
    private String result;

    /**
     * Status
     * 1. UnFinished 2. Finished
     */
    private Integer status;

    /**
     * ExtendData
     */
    private ExtendData extendData;

    /**
     * DataCheckStatus
     * 1: Enable 2: Unable
     */
    private Integer dataCheckStatus;

    /**
     * createdAt
     */
    private Long createdAt;

    /**
     * updatedAt
     */
    private Long updatedAt;

    /**
     * engagedAt
     */
    private Long engagedAt;

    @Data
    public static class ExtendData implements Serializable {
        private static final long serialVersionUID = 1L;

    }

}
