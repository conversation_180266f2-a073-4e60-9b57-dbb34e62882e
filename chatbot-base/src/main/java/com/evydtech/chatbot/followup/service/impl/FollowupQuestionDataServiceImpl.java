/**
 * FollowupQuestionServiceImpl
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2024/9/15 16:26
 * @license: Copyright © 2012 - 2024 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.evydtech.chatbot.common.enums.DataCheckStatusEnum;
import com.evydtech.chatbot.common.enums.ErrorCodeEnum;
import com.evydtech.chatbot.common.excepiton.BizException;
import com.evydtech.chatbot.common.util.UniqueIdUtil;
import com.evydtech.chatbot.followup.dao.FollowupQuestionDataDao;
import com.evydtech.chatbot.followup.domain.qo.FollowupQuestionDataQo;
import com.evydtech.chatbot.followup.domain.vo.FollowupQuestionDataVo;
import com.evydtech.chatbot.followup.entity.FollowupQuestionDataEntity;
import com.evydtech.chatbot.followup.entity.FollowupQuestionEntity;
import com.evydtech.chatbot.followup.service.FollowupQuestionDataService;
import com.mongodb.client.result.UpdateResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FollowupQuestionDataServiceImpl implements FollowupQuestionDataService {
    private final FollowupQuestionDataDao followupQuestionDataDao;

    @Autowired
    public FollowupQuestionDataServiceImpl(FollowupQuestionDataDao followupQuestionDataDao) {
        this.followupQuestionDataDao = followupQuestionDataDao;
    }

    /**
     * GetOneByUserBizIdAndType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    @Override
    public FollowupQuestionDataVo getOneByUserBizIdAndType(final FollowupQuestionDataQo qo) {
        return null;
    }


    /**
     * GetOneByQo
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    @Override
    public FollowupQuestionDataVo getOneByQo(final FollowupQuestionDataQo qo) {
        if (ObjectUtil.isNull(qo)) {
            log.error("FOLLOWUP_GET_ONE_BY_QO_PARAMS_FAILED|{}", JSON.toJSONString(qo));
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_PARAMS_FAILED.getCode());
        }
        FollowupQuestionDataEntity entity = this.followupQuestionDataDao.findOneByQo(qo);
        if (ObjectUtil.isNull(entity)) {
            return null;
        }
        FollowupQuestionDataVo resultVo = new FollowupQuestionDataVo();
        BeanUtil.copyProperties(entity, resultVo);
        return resultVo;
    }

    /**
     * GetListByType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    @Override
    public List<FollowupQuestionDataVo> getListByType(final FollowupQuestionDataQo qo) {
        if (ObjectUtil.isNull(qo.getType())) {
            log.error("FOLLOWUP_GET_LIST_BY_TYPE_PARAMS_FAILED|{}", JSON.toJSONString(qo));
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_PARAMS_FAILED.getCode());
        }
        try {
            List<FollowupQuestionDataEntity> questionEntityList = this.followupQuestionDataDao.findListByType(qo.getType());
            if (CollUtil.isEmpty(questionEntityList)) {
                return CollUtil.newArrayList();
            }
            return questionEntityList.stream().map(item -> {
                FollowupQuestionDataVo tmpVo = new FollowupQuestionDataVo();
                BeanUtil.copyProperties(item, tmpVo);
                return tmpVo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FOLLOWUP_GET_ONE_BY_TYPE_FAILED", e);
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_GET_RECORD_FAILED.getCode());
        }
    }

    /**
     * GetListByUserBizIdAndType
     *
     * @param qo FollowupQuestionDataQo
     * @return List<FollowupQuestionDataVo>
     */
    @Override
    public List<FollowupQuestionDataVo> getListByQo(final FollowupQuestionDataQo qo) {
        if (ObjectUtil.isNull(qo.getUserBizId())) {
            log.error("FOLLOWUP_GET_LIST_BY_TYPE_AND_USER_ID_PARAMS_FAILED|{}", JSON.toJSONString(qo));
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_PARAMS_FAILED.getCode());
        }
        try {
            List<FollowupQuestionDataEntity> questionEntityList = this.followupQuestionDataDao.findListByTypeAndUserId(qo.getType(), qo.getUserBizId());
            if (CollUtil.isEmpty(questionEntityList)) {
                return CollUtil.newArrayList();
            }
            return questionEntityList.stream().map(item -> {
                FollowupQuestionDataVo tmpVo = new FollowupQuestionDataVo();
                BeanUtil.copyProperties(item, tmpVo);
                return tmpVo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FOLLOWUP_GET_ONE_BY_TYPE_FAILED", e);
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_GET_RECORD_FAILED.getCode());
        }
    }

    @Override
    public FollowupQuestionDataVo create(final FollowupQuestionDataQo qo) {
        // Use dao to insert data to mongo
        if (ObjectUtil.isNull(qo)) {
            log.error("FOLLOWUP_CREATE_PARAMS_FAILED|{}", JSON.toJSONString(qo));
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_QA_DATA_CREATE_FAILED.getCode());
        }
        try {
            FollowupQuestionDataEntity entity = new FollowupQuestionDataEntity();
            BeanUtil.copyProperties(qo, entity);
            entity.setBizId(UniqueIdUtil.createBizUniqueId());
            entity.setCreatedAt(System.currentTimeMillis());
            entity.setUpdatedAt(System.currentTimeMillis());
            entity.setDataCheckStatus(DataCheckStatusEnum.ENABLE.getCode());
            FollowupQuestionDataEntity result = this.followupQuestionDataDao.insert(entity);
            FollowupQuestionDataVo followupTaskDataVo = new FollowupQuestionDataVo();
            BeanUtil.copyProperties(result, followupTaskDataVo);
            return followupTaskDataVo;
        } catch (Exception e) {
            log.error("FOLLOWUP_CREATE_FAILED", e);
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_QA_DATA_CREATE_FAILED);
        }

    }

    @Override
    public FollowupQuestionDataVo updateResultByBizId(final FollowupQuestionDataQo qo) {
        // Use followupTaskDataDao to update data to mongo by bizId
        if (ObjectUtil.isNull(qo)) {
            log.error("FOLLOWUP_UPDATE_PARAMS_FAILED|{}", qo);
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_QA_DATA_UPDATE_FAILED.getCode());
        }
        try {
            UpdateResult result = this.followupQuestionDataDao.updateOne(qo);
            this.followupQuestionDataDao.findOneByBizId(qo.getBizId());
            FollowupQuestionDataVo followupTaskDataVo = new FollowupQuestionDataVo();
            BeanUtil.copyProperties(result, followupTaskDataVo);
            return followupTaskDataVo;
        } catch (Exception e) {
            log.error("FOLLOWUP_UPDATE_FAILED", e);
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_QA_DATA_UPDATE_FAILED);
        }
    }
}
