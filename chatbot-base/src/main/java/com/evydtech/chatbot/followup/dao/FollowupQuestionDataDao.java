/**
 * FollowupTaskDao
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/5/16 20:24
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.evydtech.chatbot.chat.constant.SchemaFieldConversationConstant;
import com.evydtech.chatbot.chat.domain.entity.ConversationEntity;
import com.evydtech.chatbot.common.enums.DataCheckStatusEnum;
import com.evydtech.chatbot.followup.constant.SchemaFieldFollowupTaskConstant;
import com.evydtech.chatbot.followup.domain.qo.FollowupQuestionDataQo;
import com.evydtech.chatbot.followup.entity.FollowupQuestionDataEntity;
import com.evydtech.chatbot.followup.entity.FollowupQuestionEntity;
import com.mongodb.client.result.UpdateResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FollowupQuestionDataDao {
    @Autowired
    MongoTemplate mongoTemplate;

    /**
     * FindOneByType
     *
     * @param bizId String
     * @return FollowupTaskEntity
     */
    public FollowupQuestionEntity findOneByBizId(String bizId) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.BIZ_ID).is(bizId);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.findOne(query, FollowupQuestionEntity.class);
    }

    /**
     * FindOneByUserBizIdAndType
     *
     * @param userBizId String
     * @param type      String
     * @return FollowupTaskEntity
     */
    public FollowupQuestionEntity findOneByUserBizIdAndType(String userBizId, Integer type) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.USER_BIZ_ID).is(userBizId);
        criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.findOne(query, FollowupQuestionEntity.class);
    }

    /**
     * FindOneByType
     *
     * @param type String
     * @return FollowupTaskEntity
     */
    public FollowupQuestionEntity findOneByType(Integer type) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.findOne(query, FollowupQuestionEntity.class);
    }

    /**
     * FindOneByQo
     *
     * @param qo FollowupQuestionDataQo
     * @return FollowupTaskEntity
     */
    public FollowupQuestionDataEntity findOneByQo(FollowupQuestionDataQo qo) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (ObjectUtil.isNotNull(qo.getUserBizId())) {
            criteria.and(SchemaFieldFollowupTaskConstant.USER_BIZ_ID).is(qo.getUserBizId());
        }
        if (ObjectUtil.isNotNull(qo.getType())) {
            criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(qo.getType());
        }
        if (ObjectUtil.isNotNull(qo.getStatus())) {
            criteria.and(SchemaFieldFollowupTaskConstant.STATUS).is(qo.getStatus());
        }
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        // Sort by createdAt desc
        query.with(Sort.by(Sort.Direction.DESC, SchemaFieldFollowupTaskConstant.CREATED_AT));
        return this.mongoTemplate.findOne(query, FollowupQuestionDataEntity.class);
    }

    /**
     * FindOneByType
     *
     * @param type String
     * @return FollowupTaskEntity
     */
    public List<FollowupQuestionDataEntity> findListByType(Integer type) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.find(query, FollowupQuestionDataEntity.class);
    }

    /**
     * FindOneByType
     *
     * @param type String
     * @return FollowupTaskEntity
     */
    public List<FollowupQuestionDataEntity> findListByTypeAndUserId(Integer type, String userBizId) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (ObjectUtil.isNotNull(type)) {
            criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        }
        if (StrUtil.isNotBlank(userBizId)) {
            criteria.and(SchemaFieldFollowupTaskConstant.USER_BIZ_ID).is(userBizId);
        }
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.find(query, FollowupQuestionDataEntity.class);
    }

    public FollowupQuestionDataEntity insert(FollowupQuestionDataEntity followupTaskDataEntity) {
        return this.mongoTemplate.insert(followupTaskDataEntity);
    }

    public UpdateResult updateOne(FollowupQuestionDataQo qo) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldConversationConstant.BIZ_ID).is(qo.getBizId());
        criteria.and(SchemaFieldConversationConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);

        Update update = new Update();
        update.set(SchemaFieldFollowupTaskConstant.STATUS, qo.getStatus());
        update.set(SchemaFieldFollowupTaskConstant.RESULT, qo.getResult());
        update.set(SchemaFieldConversationConstant.UPDATED_AT, System.currentTimeMillis());
        return this.mongoTemplate.upsert(query, update, FollowupQuestionDataEntity.class);
    }
}
