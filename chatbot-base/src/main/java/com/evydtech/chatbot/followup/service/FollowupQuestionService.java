/**
 * FollowupTaskService
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/5/16 19:34
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.service;

import com.evydtech.chatbot.followup.domain.qo.FollowupQuestionQo;
import com.evydtech.chatbot.followup.domain.vo.FollowupQuestionVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface FollowupQuestionService {
    /**
     * GetOneByUserBizIdAndType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    FollowupQuestionVo getOneByUserBizIdAndType(FollowupQuestionQo qo);

    /**
     * GetOneByType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    FollowupQuestionVo getOneByType(FollowupQuestionQo qo);


    /**
     * GetListByType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    List<FollowupQuestionVo> getListByType(FollowupQuestionQo qo);
}
