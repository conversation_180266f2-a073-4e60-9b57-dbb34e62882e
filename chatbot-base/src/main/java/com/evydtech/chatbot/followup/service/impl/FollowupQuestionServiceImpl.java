/**
 * FollowupQuestionServiceImpl
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2024/9/15 16:26
 * @license: Copyright © 2012 - 2024 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.evydtech.chatbot.common.enums.ErrorCodeEnum;
import com.evydtech.chatbot.common.excepiton.BizException;
import com.evydtech.chatbot.followup.dao.FollowupQuestionDao;
import com.evydtech.chatbot.followup.domain.qo.FollowupQuestionQo;
import com.evydtech.chatbot.followup.domain.vo.FollowupQuestionVo;
import com.evydtech.chatbot.followup.entity.FollowupQuestionEntity;
import com.evydtech.chatbot.followup.service.FollowupQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FollowupQuestionServiceImpl implements FollowupQuestionService {
    private final FollowupQuestionDao followupTaskDao;

    @Autowired
    public FollowupQuestionServiceImpl(FollowupQuestionDao followupTaskDao) {
        this.followupTaskDao = followupTaskDao;
    }

    /**
     * GetOneByUserBizIdAndType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    @Override
    public FollowupQuestionVo getOneByUserBizIdAndType(final FollowupQuestionQo qo) {
        return null;
    }

    /**
     * GetOneByType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    @Override
    public FollowupQuestionVo getOneByType(final FollowupQuestionQo qo) {
        if (ObjectUtil.isNull(qo.getType())) {
            log.error("FOLLOWUP_GET_ONE_BY_TYPE_PARAMS_FAILED|{}", JSON.toJSONString(qo));
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_PARAMS_FAILED.getCode());
        }
        if (ObjectUtil.isNull(qo.getNo())) {
            qo.setNo(1);
        }
        try {
            FollowupQuestionEntity questionEntity = this.followupTaskDao.findListByTypeAndNo(qo.getType(), qo.getNo());
            if (ObjectUtil.isEmpty(questionEntity)) {
                return null;
            }
            FollowupQuestionVo followupTaskVo = new FollowupQuestionVo();
            BeanUtil.copyProperties(questionEntity, followupTaskVo);
            return followupTaskVo;
        } catch (Exception e) {
            log.error("FOLLOWUP_GET_ONE_BY_TYPE_FAILED", e);
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_GET_RECORD_FAILED.getCode());
        }
    }

    /**
     * GetListByType
     *
     * @param qo FollowupTaskQo
     * @return FollowupTaskVo
     */
    @Override
    public List<FollowupQuestionVo> getListByType(final FollowupQuestionQo qo) {
        if (ObjectUtil.isNull(qo.getType())) {
            log.error("FOLLOWUP_GET_ONE_BY_TYPE_PARAMS_FAILED|{}", JSON.toJSONString(qo));
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_PARAMS_FAILED.getCode());
        }
        try {
            List<FollowupQuestionEntity> questionEntityList = this.followupTaskDao.findListByType(qo.getType());
            if (CollUtil.isEmpty(questionEntityList)) {
                return CollUtil.newArrayList();
            }
            return questionEntityList.stream().map(item -> {
                FollowupQuestionVo followupTaskVo = new FollowupQuestionVo();
                BeanUtil.copyProperties(item, followupTaskVo);
                return followupTaskVo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("FOLLOWUP_GET_ONE_BY_TYPE_FAILED", e);
            throw new BizException(ErrorCodeEnum.BIZ_FOLLOWUP_GET_RECORD_FAILED.getCode());
        }
    }
}
