/**
 * FollowupTaskService
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/5/16 19:34
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.service;

import com.evydtech.chatbot.followup.domain.qo.FollowupQuestionDataQo;
import com.evydtech.chatbot.followup.domain.vo.FollowupQuestionDataVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface FollowupQuestionDataService {
    /**
     * GetOneByUserBizIdAndType
     *
     * @param qo FollowupQuestionDataQo
     * @return FollowupQuestionDataVo
     */
    FollowupQuestionDataVo getOneByUserBizIdAndType(FollowupQuestionDataQo qo);

    /**
     * GetListByQo
     *
     * @param qo FollowupQuestionDataQo
     * @return List<FollowupQuestionDataVo>
     */
    List<FollowupQuestionDataVo> getListByQo(FollowupQuestionDataQo qo);

    /**
     * GetOneByType
     *
     * @param qo FollowupQuestionDataQo
     * @return FollowupQuestionDataVo
     */
    FollowupQuestionDataVo getOneByQo(FollowupQuestionDataQo qo);

    /**
     * GetListByType
     *
     * @param qo FollowupQuestionDataQo
     * @return List<FollowupQuestionDataVo>
     */
    List<FollowupQuestionDataVo> getListByType(FollowupQuestionDataQo qo);

    FollowupQuestionDataVo create(FollowupQuestionDataQo qo);

    FollowupQuestionDataVo updateResultByBizId(FollowupQuestionDataQo qo);
}
